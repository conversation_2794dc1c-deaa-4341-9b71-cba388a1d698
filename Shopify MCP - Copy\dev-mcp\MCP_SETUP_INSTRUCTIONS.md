# Shopify MCP Server Setup - Multiple Integration Options

## ✅ Setup Complete!

Your Shopify MCP server has been successfully configured and can be used with multiple clients including <PERSON> for <PERSON>, <PERSON>, Cursor, and **OpenRouter**!

## What's Been Done

1. **Built the project**: The TypeScript source has been compiled to `dist/index.js`
2. **Configured VS Code settings**: Added MCP server configuration to `.vscode/settings.json`
3. **Created alternative configurations**: Additional config files for different use cases

## Current Configuration

The MCP server is now configured in your VS Code workspace settings (`.vscode/settings.json`) with:

```json
{
  "claude.mcpServers": {
    "shopify-dev-mcp": {
      "command": "node",
      "args": ["${workspaceFolder}/dist/index.js"],
      "env": {
        "OPT_OUT_INSTRUMENTATION": "true"
      }
    }
  }
}
```

## How to Use

1. **Restart VS Code** to ensure the new MCP server configuration is loaded
2. **Open Claude for Code** in VS Code
3. The Shopify MCP server should now be available with these tools:
   - `search_dev_docs` - Search shopify.dev documentation
   - `introspect_admin_schema` - Access and search Shopify Admin GraphQL schema
   - `fetch_docs_by_path` - Retrieve documents from shopify.dev
   - `get_started` - Get started with Shopify APIs (Admin, Functions, etc.)

## Available Prompts

- `shopify_admin_graphql` - Help you write GraphQL operations for the Shopify Admin API

## Alternative Configurations

### With Polaris Support (Experimental)

If you want to enable Polaris Web Components documentation, replace your `.vscode/settings.json` content with the content from `.vscode/settings_with_polaris.json`.

### For Claude Desktop (if needed later)

- `claude_desktop_config.json` - Basic configuration for Claude Desktop
- `claude_desktop_config_with_polaris.json` - With Polaris support enabled

## Troubleshooting

1. **Server not starting**: Make sure Node.js is installed and accessible
2. **Changes not reflected**: Restart VS Code after modifying the configuration
3. **Permission issues**: Ensure the `dist/index.js` file is executable

## Testing the Server

To test if the server works correctly, you can run:

```bash
node dist/index.js
```

The server should start and display a message like "Shopify Dev MCP Server v1.1.0 running on stdio".

## Environment Variables

- `OPT_OUT_INSTRUMENTATION=true` - Disables telemetry collection
- `POLARIS_UNIFIED=true` - Enables Polaris Web Components documentation (experimental)

## Next Steps

1. Restart VS Code
2. Open Claude for Code
3. Try using one of the Shopify tools, for example:
   - Ask Claude to "search for product creation in Shopify docs"
   - Ask Claude to "help me write a GraphQL query to fetch products"

The MCP server will provide Claude with access to Shopify documentation and schema information to help you with Shopify development tasks.

---

## 🚀 Using with OpenRouter

### ✅ Working Solution: Direct Integration

The **simplest and most reliable** way to use OpenRouter with Shopify context:

1. **Get your OpenRouter API key** from https://openrouter.ai/keys

2. **Set your API key**:

   ```bash
   export OPENROUTER_API_KEY="your_api_key_here"
   ```

3. **Run the working integration**:
   ```bash
   npm run openrouter
   ```

This provides immediate Shopify development assistance using **Google Gemini 2.5 Pro Preview** with built-in Shopify context!

### 🎯 **Model Options:**

- **Default**: `npm run openrouter` (uses Gemini 2.5 Pro Preview)
- **Enhanced**: `npm run openrouter-enhanced [model]` (configurable model)
- **Compare**: `npm run compare-models` (test multiple models)

**Examples:**

```bash
npm run openrouter-enhanced gemini    # Google Gemini 2.5 Pro Preview
npm run openrouter-enhanced claude    # Anthropic Claude 3.5 Sonnet
npm run openrouter-enhanced gpt4      # OpenAI GPT-4
npm run openrouter-enhanced llama     # Meta Llama 3.1 405B
```

### Option 2: HTTP API Wrapper (Advanced)

For more complex integrations, you can try the HTTP API wrapper:

1. **Install additional dependencies**:

   ```bash
   npm install express cors node-fetch
   ```

2. **Start the HTTP API**:

   ```bash
   npm run api
   ```

3. **Run the example**:
   ```bash
   npm run example
   ```

**Note**: The HTTP API wrapper has some technical issues with MCP client connections, but the direct integration works perfectly!

### HTTP API Endpoints

When running the HTTP API (`npm run api`), you get these endpoints:

- `GET /health` - Check if the service is running
- `GET /tools` - List all available Shopify tools
- `POST /search` - Search Shopify documentation
  ```json
  { "query": "product creation" }
  ```
- `POST /schema` - Query GraphQL schema
  ```json
  { "query": "Product", "filter": ["types"] }
  ```
- `POST /get-started` - Get started guides
  ```json
  { "api": "Admin API" }
  ```
- `POST /docs` - Fetch specific documentation
  ```json
  { "path": "/docs/api/admin-graphql/latest/objects/product" }
  ```

### Example Usage with OpenRouter

```javascript
import { OpenRouterShopifyClient } from "./openrouter_usage_example.js";

const client = new OpenRouterShopifyClient(process.env.OPENROUTER_API_KEY);

// Ask Shopify questions with context from the MCP server
const answer = await client.askShopifyQuestion(
  "How do I create a product with variants using GraphQL?",
);
```

### Supported Models

The OpenRouter integration works with any model available on OpenRouter:

- `google/gemini-2.5-pro-preview` (default)
- `anthropic/claude-3.5-sonnet`
- `openai/gpt-4`
- `meta-llama/llama-3.1-405b`
- And many more!

---

## 🔧 Integration Options Summary

| Method                     | Best For            | Setup Complexity |
| -------------------------- | ------------------- | ---------------- |
| **Claude for Code**        | VS Code users       | Easy             |
| **Claude Desktop**         | Desktop app users   | Easy             |
| **Cursor**                 | Cursor IDE users    | Easy             |
| **OpenRouter HTTP API**    | Any LLM provider    | Medium           |
| **Direct MCP Integration** | Custom applications | Advanced         |
