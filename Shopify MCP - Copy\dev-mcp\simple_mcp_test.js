#!/usr/bin/env node

/**
 * Simple test to check MCP server connection
 */

import { spawn } from "child_process";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";

console.log("Testing MCP server connection...");

async function testMCPConnection() {
  try {
    console.log("1. Starting MCP server process...");

    // Start the MCP server process
    const serverProcess = spawn("node", ["./dist/index.js"], {
      stdio: ["pipe", "pipe", "pipe"],
      env: {
        ...process.env,
        OPT_OUT_INSTRUMENTATION: "true",
      },
    });

    console.log("2. MCP server process started");

    // Handle server process errors
    serverProcess.on("error", (error) => {
      console.error("❌ MCP Server process error:", error);
    });

    serverProcess.stderr.on("data", (data) => {
      console.log("MCP Server stderr:", data.toString());
    });

    // Give the server a moment to start
    await new Promise((resolve) => setTimeout(resolve, 1000));

    console.log("3. Creating MCP client...");

    // Create MCP client and transport
    const mcpTransport = new StdioClientTransport();

    const mcpClient = new Client(
      {
        name: "test-client",
        version: "1.0.0",
      },
      {
        capabilities: {},
      },
    );

    console.log("4. Connecting to MCP server...");

    // Connect to the MCP server
    await mcpClient.connect(mcpTransport);
    console.log("✅ Connected to Shopify MCP Server successfully!");

    // Test listing tools
    console.log("5. Testing tool listing...");
    const tools = await mcpClient.listTools();
    console.log(
      "✅ Available tools:",
      tools.tools.map((t) => t.name),
    );

    // Clean up
    serverProcess.kill();
    console.log("✅ Test completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

testMCPConnection();
