{% extends "base.html" %}

{% block title %}Dashboard - Shopify AI Manager{% endblock %}

{% block page_title %}Dashboard{% endblock %}
{% block page_subtitle %}Overview of your AI-powered Shopify management{% endblock %}

{% block content %}
<div class="row">
    <!-- Welcome Card -->
    <div class="col-12 mb-4">
        <div class="card module-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="card-title mb-3">
                            <i class="fas fa-robot text-primary"></i> 
                            Welcome to Shopify AI Manager
                        </h2>
                        <p class="card-text lead">
                            Manage your Shopify store with the power of AI. Get intelligent assistance for SEO, advertising, email marketing, and customer support.
                        </p>
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('connect') }}" class="btn btn-shopify">
                                <i class="fas fa-plug"></i> Connect Your Store
                            </a>
                            <button class="btn btn-outline-secondary" onclick="checkMCPStatus()">
                                <i class="fas fa-server"></i> Check MCP Status
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <i class="fas fa-store" style="font-size: 4rem; color: var(--shopify-green); opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Module Cards -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card module-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-search" style="font-size: 2.5rem; color: #007bff;"></i>
                </div>
                <h5 class="card-title">SEO Optimization</h5>
                <p class="card-text">Improve your store's search rankings with AI-powered SEO recommendations.</p>
                <a href="{{ url_for('seo_module') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-right"></i> Open SEO
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card module-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-bullhorn" style="font-size: 2.5rem; color: #fd7e14;"></i>
                </div>
                <h5 class="card-title">Advertising</h5>
                <p class="card-text">Create and optimize ad campaigns with intelligent targeting and copy suggestions.</p>
                <a href="{{ url_for('ads_module') }}" class="btn btn-outline-warning">
                    <i class="fas fa-arrow-right"></i> Open Ads
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card module-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-envelope" style="font-size: 2.5rem; color: #198754;"></i>
                </div>
                <h5 class="card-title">Email Marketing</h5>
                <p class="card-text">Design effective email campaigns and automation workflows with AI assistance.</p>
                <a href="{{ url_for('email_module') }}" class="btn btn-outline-success">
                    <i class="fas fa-arrow-right"></i> Open Email
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card module-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-headset" style="font-size: 2.5rem; color: #6f42c1;"></i>
                </div>
                <h5 class="card-title">Customer Support</h5>
                <p class="card-text">Enhance customer service with AI chatbots and automated support responses.</p>
                <a href="{{ url_for('support_module') }}" class="btn btn-outline-info">
                    <i class="fas fa-arrow-right"></i> Open Support
                </a>
            </div>
        </div>
    </div>
    
    <!-- System Status -->
    <div class="col-12 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <div class="me-3">
                                <i class="fas fa-server text-primary"></i>
                            </div>
                            <div>
                                <strong>MCP Server</strong><br>
                                <span id="mcp-status" class="text-muted">Checking...</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <div class="me-3">
                                <i class="fas fa-robot text-success"></i>
                            </div>
                            <div>
                                <strong>AI Assistant</strong><br>
                                <span id="ai-status" class="text-muted">Ready</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <div class="me-3">
                                <i class="fas fa-store text-warning"></i>
                            </div>
                            <div>
                                <strong>Shopify Connection</strong><br>
                                <span id="shopify-status" class="text-muted">Not Connected</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-primary me-2" onclick="checkMCPStatus()">
                        <i class="fas fa-sync"></i> Refresh Status
                    </button>
                    <button class="btn btn-sm btn-outline-success me-2" onclick="startMCP()">
                        <i class="fas fa-play"></i> Start MCP
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="stopMCP()">
                        <i class="fas fa-stop"></i> Stop MCP
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-12">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Store Management</h6>
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action" onclick="quickAction('analyze-products')">
                                <i class="fas fa-box"></i> Analyze Products
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="quickAction('check-seo')">
                                <i class="fas fa-search"></i> Quick SEO Check
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="quickAction('review-orders')">
                                <i class="fas fa-shopping-cart"></i> Review Recent Orders
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>AI Assistance</h6>
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action" onclick="quickAction('marketing-ideas')">
                                <i class="fas fa-lightbulb"></i> Get Marketing Ideas
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="quickAction('content-suggestions')">
                                <i class="fas fa-edit"></i> Content Suggestions
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="quickAction('performance-tips')">
                                <i class="fas fa-chart-line"></i> Performance Tips
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Action Modal -->
<div class="modal fade" id="quickActionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickActionTitle">Quick Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="quickActionContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Processing your request...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Check MCP server status
    function checkMCPStatus() {
        fetch('/api/mcp-status')
            .then(response => response.json())
            .then(data => {
                const mcpStatus = document.getElementById('mcp-status');
                if (data.connected) {
                    mcpStatus.innerHTML = '<span class="text-success">Connected</span>';
                } else {
                    mcpStatus.innerHTML = '<span class="text-danger">Disconnected</span>';
                }
            })
            .catch(error => {
                document.getElementById('mcp-status').innerHTML = '<span class="text-danger">Error</span>';
                console.error('Error checking MCP status:', error);
            });
    }
    
    // Start MCP server
    function startMCP() {
        fetch('/api/start-mcp', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    checkMCPStatus();
                    alert('MCP Server started successfully!');
                } else {
                    alert('Failed to start MCP Server');
                }
            })
            .catch(error => {
                alert('Error starting MCP Server: ' + error.message);
            });
    }
    
    // Stop MCP server
    function stopMCP() {
        fetch('/api/stop-mcp', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                checkMCPStatus();
                alert('MCP Server stopped');
            })
            .catch(error => {
                alert('Error stopping MCP Server: ' + error.message);
            });
    }
    
    // Quick actions
    function quickAction(action) {
        const modal = new bootstrap.Modal(document.getElementById('quickActionModal'));
        const title = document.getElementById('quickActionTitle');
        const content = document.getElementById('quickActionContent');
        
        // Set title based on action
        const actionTitles = {
            'analyze-products': 'Product Analysis',
            'check-seo': 'SEO Quick Check',
            'review-orders': 'Order Review',
            'marketing-ideas': 'Marketing Ideas',
            'content-suggestions': 'Content Suggestions',
            'performance-tips': 'Performance Tips'
        };
        
        title.textContent = actionTitles[action] || 'Quick Action';
        
        // Show loading
        content.innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Processing your request...</p>
            </div>
        `;
        
        modal.show();
        
        // Send AI request
        const prompts = {
            'analyze-products': 'Analyze my Shopify store products and provide insights on inventory, pricing, and optimization opportunities.',
            'check-seo': 'Perform a quick SEO check on my Shopify store and provide immediate improvement recommendations.',
            'review-orders': 'Review recent orders and provide insights on sales patterns and customer behavior.',
            'marketing-ideas': 'Generate creative marketing ideas for my Shopify store based on current trends.',
            'content-suggestions': 'Suggest content ideas for my store including blog posts, product descriptions, and social media.',
            'performance-tips': 'Provide performance optimization tips for my Shopify store to improve speed and conversions.'
        };
        
        fetch('/api/ai-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: prompts[action],
                module: 'general'
            })
        })
        .then(response => response.json())
        .then(data => {
            content.innerHTML = `<div class="ai-response">${data.response.replace(/\n/g, '<br>')}</div>`;
        })
        .catch(error => {
            content.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        });
    }
    
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        checkMCPStatus();
        
        // Update statuses periodically
        setInterval(checkMCPStatus, 30000);
    });
</script>
{% endblock %}
