# Shopify AI Manager

A Python Flask system that allows people to control their Shopify storefronts using AI. This system integrates with the Shopify MCP (Model Context Protocol) server to provide intelligent assistance for store management.

## Features

### 🤖 AI-Powered Modules
- **SEO Optimization** - Improve search rankings with AI recommendations
- **Advertising Management** - Create and optimize ad campaigns
- **Email Marketing** - Design effective email campaigns and automation
- **Customer Support** - AI chatbots and support automation

### 🔌 Shopify Integration
- **MCP Server Integration** - Uses Shopify's Model Context Protocol server
- **Store Connection** - Simple connection system for Shopify stores
- **Real-time Data** - Access to Shopify documentation and schema
- **AI Context** - Shopify-specific context for better AI responses

### 🎯 Key Capabilities
- **Google Gemini 2.5 Pro Preview** - Latest AI model for intelligent responses
- **Interactive Chat** - AI assistants for each module
- **Quick Actions** - Pre-built prompts for common tasks
- **Campaign Builders** - AI-generated campaigns and content
- **Performance Analytics** - Track and optimize results

## Prerequisites

1. **Node.js** - Required for the Shopify MCP server
2. **Python 3.8+** - For the Flask application
3. **Shopify Store** - With Admin API access
4. **OpenRouter API Key** - For AI functionality

## Installation

### 1. Setup Shopify MCP Server

First, ensure the Shopify MCP server is built and ready:

```bash
# From the main project directory
npm install
npm run build
```

### 2. Install Python Dependencies

```bash
cd shopify_ai_manager
pip install -r requirements.txt
```

### 3. Set Environment Variables

```bash
# Set your OpenRouter API key
export OPENROUTER_API_KEY="your_openrouter_api_key_here"

# Optional: Set Flask secret key for production
export SECRET_KEY="your_secret_key_here"
```

### 4. Run the Application

```bash
# Using the startup script (recommended)
python run.py

# Or directly with Flask
python app.py
```

The application will be available at `http://localhost:5000`

## Usage

### 1. Connect Your Shopify Store

1. Go to **Connect Store** in the sidebar
2. Follow the setup guide to create a Shopify private app
3. Enter your store URL and Admin API access token
4. Click **Connect Store**

### 2. Use AI Modules

Each module provides:
- **AI Chat Assistant** - Ask questions and get intelligent responses
- **Quick Actions** - Pre-built prompts for common tasks
- **Tools & Builders** - Generate campaigns, content, and strategies

### 3. Available Modules

#### SEO Optimization
- SEO audits and recommendations
- Keyword research and analysis
- Meta tag optimization
- Content strategy development

#### Advertising Management
- Campaign creation and optimization
- Ad copy generation
- Audience targeting strategies
- Performance analysis

#### Email Marketing
- Email campaign creation
- Subject line optimization
- Automation workflow setup
- Segmentation strategies

#### Customer Support
- Chatbot setup and training
- Response template creation
- FAQ management
- Support ticket analysis

## Configuration

### Shopify Store Connection

To connect your Shopify store, you need:

1. **Store URL** - Your myshopify.com URL
2. **Admin API Access Token** - From a private app

#### Required Permissions

Your private app needs these Admin API permissions:

**Read Permissions:**
- Products
- Orders
- Customers
- Analytics
- Online Store

**Write Permissions:**
- Products
- Online Store
- Marketing Events
- Content

### OpenRouter Configuration

The system uses OpenRouter for AI functionality with Google Gemini 2.5 Pro Preview as the default model.

Get your API key from: https://openrouter.ai/keys

## Architecture

```
shopify_ai_manager/
├── app.py                 # Main Flask application
├── run.py                 # Startup script
├── requirements.txt       # Python dependencies
├── templates/
│   ├── base.html         # Base template
│   ├── index.html        # Dashboard
│   ├── connect.html      # Store connection
│   └── modules/          # Module templates
│       ├── seo.html      # SEO module
│       ├── ads.html      # Advertising module
│       ├── email.html    # Email marketing module
│       └── support.html  # Customer support module
└── README.md             # This file
```

## API Endpoints

### Store Management
- `POST /api/connect-store` - Connect Shopify store
- `POST /api/disconnect-store` - Disconnect store
- `GET /api/store-status` - Get connection status

### AI Chat
- `POST /api/ai-chat` - Send message to AI assistant

### MCP Server
- `GET /api/mcp-status` - Get MCP server status
- `POST /api/start-mcp` - Start MCP server
- `POST /api/stop-mcp` - Stop MCP server

## Development

### Adding New Modules

1. Create a new template in `templates/modules/`
2. Add route in `app.py`
3. Update sidebar navigation in `base.html`
4. Implement module-specific AI prompts

### Customizing AI Responses

Modify the `system_prompts` in the `AIAssistant` class to customize AI behavior for each module.

### Extending MCP Integration

The `ShopifyMCPClient` class can be extended to add more MCP server functionality:
- Additional tool calls
- Real-time data fetching
- Advanced schema introspection

## Troubleshooting

### Common Issues

1. **MCP Server Not Found**
   ```bash
   cd ../  # Go to main project directory
   npm run build
   ```

2. **OpenRouter API Errors**
   - Check your API key is set correctly
   - Verify you have credits in your OpenRouter account

3. **Shopify Connection Issues**
   - Verify your store URL format
   - Check Admin API permissions
   - Ensure access token is valid

### Debug Mode

Run with debug mode for detailed error messages:

```bash
export FLASK_DEBUG=1
python app.py
```

## Security Notes

- Store credentials are kept in session (use database in production)
- Admin API tokens should have minimal required permissions
- Use HTTPS in production
- Set a strong SECRET_KEY for production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Check the troubleshooting section
- Review the Shopify MCP server documentation
- Open an issue on the repository
