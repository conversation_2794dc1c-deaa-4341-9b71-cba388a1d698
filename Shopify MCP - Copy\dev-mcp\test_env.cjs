#!/usr/bin/env node

console.log('Testing environment variables...');
console.log('process.env.OPENROUTER_API_KEY:', process.env.OPENROUTER_API_KEY);
console.log('Length:', process.env.OPENROUTER_API_KEY ? process.env.OPENROUTER_API_KEY.length : 'undefined');
console.log('Type:', typeof process.env.OPENROUTER_API_KEY);

if (process.env.OPENROUTER_API_KEY) {
  console.log('✅ API key is set!');
  console.log('First 10 chars:', process.env.OPENROUTER_API_KEY.substring(0, 10) + '...');
} else {
  console.log('❌ API key is NOT set');
}
