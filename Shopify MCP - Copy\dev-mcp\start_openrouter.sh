#!/bin/bash

# Quick start script for OpenRouter integration

echo "🚀 Starting Shopify MCP Server with OpenRouter integration"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Check if OpenRouter API key is set
if [ -z "$OPENROUTER_API_KEY" ]; then
    echo "❌ OPENROUTER_API_KEY environment variable is not set."
    echo "Please set it with: export OPENROUTER_API_KEY='your_api_key_here'"
    echo "Get your API key from: https://openrouter.ai/keys"
    exit 1
fi

echo "✅ Node.js found: $(node --version)"
echo "✅ npm found: $(npm --version)"
echo "✅ OpenRouter API key is set"
echo ""

# Install dependencies if needed
echo "📦 Installing dependencies..."
npm install

# Build the project
echo "🔨 Building the project..."
npm run build

# Check if build was successful
if [ ! -f "dist/index.js" ]; then
    echo "❌ Build failed. dist/index.js not found."
    exit 1
fi

echo "✅ Build successful"
echo ""

# Start the HTTP API in the background
echo "🌐 Starting HTTP API server..."
npm run api &
API_PID=$!

# Wait for the API to be ready
echo "⏳ Waiting for API to be ready..."
sleep 3

# Check if API is running
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ HTTP API is running on http://localhost:3000"
    echo ""
    
    # Run the working OpenRouter integration
    echo "🤖 Running OpenRouter integration..."
    npm run openrouter
    
    # Clean up
    echo ""
    echo "🧹 Cleaning up..."
    kill $API_PID
    echo "✅ Done!"
else
    echo "❌ Failed to start HTTP API"
    kill $API_PID 2>/dev/null
    exit 1
fi
