#!/usr/bin/env powershell
# VS Code Extension Fix Script

Write-Host "🔧 VS Code Extension Troubleshooting Script" -ForegroundColor Cyan
Write-Host "=" * 50

# Step 1: Check VS Code version
Write-Host "`n1️⃣ Checking VS Code version..." -ForegroundColor Yellow
$vscodeVersion = code --version
Write-Host "VS Code Version: $($vscodeVersion[0])" -ForegroundColor Green

# Step 2: List currently installed extensions
Write-Host "`n2️⃣ Listing installed extensions..." -ForegroundColor Yellow
$extensions = code --list-extensions
Write-Host "Found $($extensions.Count) extensions installed" -ForegroundColor Green

# Step 3: Check for common problematic extensions
Write-Host "`n3️⃣ Checking for common problematic extensions..." -ForegroundColor Yellow
$problematicExtensions = @(
    "ms-python.python",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-json",
    "ms-vscode.powershell"
)

foreach ($ext in $problematicExtensions) {
    if ($extensions -contains $ext) {
        Write-Host "Found potentially problematic extension: $ext" -ForegroundColor Orange
    }
}

# Step 4: Clear VS Code cache
Write-Host "`n4️⃣ Clearing VS Code cache..." -ForegroundColor Yellow
$vscodeUserDir = "$env:USERPROFILE\.vscode"
$extensionsDir = "$vscodeUserDir\extensions"
$logsDir = "$vscodeUserDir\logs"

if (Test-Path $logsDir) {
    Write-Host "Clearing logs directory..." -ForegroundColor Blue
    Remove-Item "$logsDir\*" -Recurse -Force -ErrorAction SilentlyContinue
}

# Step 5: Restart VS Code with safe mode
Write-Host "`n5️⃣ Instructions for manual steps:" -ForegroundColor Yellow
Write-Host "Please follow these steps manually:" -ForegroundColor White
Write-Host "1. Close VS Code completely" -ForegroundColor Cyan
Write-Host "2. Press Win+R, type 'code --disable-extensions' and press Enter" -ForegroundColor Cyan
Write-Host "3. If VS Code opens without errors, the issue is with an extension" -ForegroundColor Cyan
Write-Host "4. Go to Extensions panel (Ctrl+Shift+X)" -ForegroundColor Cyan
Write-Host "5. Disable extensions one by one and restart VS Code" -ForegroundColor Cyan

# Step 6: Alternative fix - Reset VS Code settings
Write-Host "`n6️⃣ Alternative: Reset VS Code settings" -ForegroundColor Yellow
Write-Host "If the above doesn't work, you can reset VS Code settings:" -ForegroundColor White
Write-Host "Settings file location: $env:APPDATA\Code\User\settings.json" -ForegroundColor Cyan

Write-Host "`n✅ Script completed. Follow the manual steps above." -ForegroundColor Green
Write-Host "If issues persist, try reinstalling VS Code." -ForegroundColor Yellow
