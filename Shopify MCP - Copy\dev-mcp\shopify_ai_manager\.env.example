# Shopify AI Manager Environment Variables
# Copy this file to .env and fill in your actual values

# OpenRouter AI API Key (required for AI features)
# Get this from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Flask Secret Key (change this in production)
SECRET_KEY=your_secret_key_here

# Shopify App Credentials
SHOPIFY_API_KEY=a7dadc9d25078a098f0d5aabcf055413
SHOPIFY_API_SECRET=a8084ee0de0188b16a12dfa76e7e9208

# Default Shopify Store (optional)
SHOPIFY_STORE_URL=yourstore.myshopify.com
SHOPIFY_ACCESS_TOKEN=your_admin_api_access_token_here

# Development Settings
FLASK_ENV=development
FLASK_DEBUG=true
