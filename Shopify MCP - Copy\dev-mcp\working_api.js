#!/usr/bin/env node

/**
 * Working Shopify MCP HTTP API
 * Simplified version that works around the MCP client issues
 */

import express from 'express';
import cors from 'cors';
import { spawn } from 'child_process';

class SimpleShopifyAPI {
  constructor() {
    this.app = express();
    this.mcpProcess = null;
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
    
    // Logging middleware
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'ok', 
        mcpRunning: !!this.mcpProcess,
        timestamp: new Date().toISOString()
      });
    });

    // Simple test endpoint
    this.app.get('/test', (req, res) => {
      res.json({ 
        message: 'API is working!',
        availableEndpoints: [
          'GET /health - Health check',
          'GET /test - Test endpoint',
          'POST /search - Search Shopify docs (coming soon)',
        ]
      });
    });

    // Placeholder for search (we'll implement this differently)
    this.app.post('/search', (req, res) => {
      const { query } = req.body;
      
      if (!query) {
        return res.status(400).json({ error: 'Query is required' });
      }

      // For now, return a mock response
      res.json({ 
        result: {
          message: `Search functionality for "${query}" is being implemented`,
          suggestion: 'Try using the Shopify MCP server directly with Claude for Code or Claude Desktop for now'
        }
      });
    });
  }

  async startMCPServer() {
    console.log('Starting Shopify MCP Server...');
    
    try {
      // Start the MCP server process
      this.mcpProcess = spawn('node', ['./dist/index.js'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          OPT_OUT_INSTRUMENTATION: 'true'
        }
      });

      // Handle server process events
      this.mcpProcess.on('error', (error) => {
        console.error('MCP Server process error:', error);
      });

      this.mcpProcess.on('exit', (code) => {
        console.log(`MCP Server process exited with code ${code}`);
      });

      this.mcpProcess.stderr.on('data', (data) => {
        console.log('MCP Server:', data.toString().trim());
      });

      // Give the server a moment to start
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('✅ MCP Server started successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to start MCP Server:', error);
      return false;
    }
  }

  async start(port = 3000) {
    try {
      console.log('🚀 Starting Shopify API...');
      
      // Start the MCP server
      const mcpStarted = await this.startMCPServer();
      
      if (!mcpStarted) {
        console.log('⚠️  MCP Server failed to start, but API will still run with limited functionality');
      }
      
      // Start the HTTP server
      this.app.listen(port, () => {
        console.log(`✅ Shopify API running on http://localhost:${port}`);
        console.log('Available endpoints:');
        console.log('  GET  /health - Health check');
        console.log('  GET  /test - Test endpoint');
        console.log('  POST /search - Search (placeholder)');
        console.log('');
        console.log('💡 For full MCP functionality, use:');
        console.log('   - Claude for Code (VS Code extension)');
        console.log('   - Claude Desktop');
        console.log('   - Cursor IDE');
      });

      // Graceful shutdown
      process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down...');
        if (this.mcpProcess) {
          this.mcpProcess.kill();
        }
        process.exit(0);
      });

    } catch (error) {
      console.error('❌ Failed to start API:', error);
      process.exit(1);
    }
  }
}

// Start the API if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const api = new SimpleShopifyAPI();
  const port = process.env.PORT || 3000;
  api.start(port);
}

export { SimpleShopifyAPI };
