#!/bin/bash

# Shopify AI Manager Setup Script

echo "🚀 Setting up Shopify AI Manager..."
echo "=================================="

# Check if we're in the right directory
if [ ! -f "shopify_ai_manager/app.py" ]; then
    echo "❌ Please run this script from the main project directory"
    exit 1
fi

# Build the MCP server
echo "📦 Building Shopify MCP Server..."
npm install
npm run build

if [ ! -f "dist/index.js" ]; then
    echo "❌ Failed to build MCP server"
    exit 1
fi

echo "✅ MCP server built successfully"

# Setup Python environment
echo "🐍 Setting up Python environment..."
cd shopify_ai_manager

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

echo "✅ Python environment setup complete"

# Check environment variables
echo "🔑 Checking environment variables..."

if [ -z "$OPENROUTER_API_KEY" ]; then
    echo "⚠️  OPENROUTER_API_KEY is not set"
    echo "Please set it with: export OPENROUTER_API_KEY='your_api_key_here'"
    echo "Get your API key from: https://openrouter.ai/keys"
else
    echo "✅ OPENROUTER_API_KEY is set"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "To start the application:"
echo "  cd shopify_ai_manager"
echo "  source venv/bin/activate"
echo "  python run.py"
echo ""
echo "Then open http://localhost:5000 in your browser"
echo ""
echo "📚 Next steps:"
echo "1. Get your OpenRouter API key from https://openrouter.ai/keys"
echo "2. Set the environment variable: export OPENROUTER_API_KEY='your_key'"
echo "3. Connect your Shopify store using the Connect Store page"
echo "4. Start using AI-powered store management!"
