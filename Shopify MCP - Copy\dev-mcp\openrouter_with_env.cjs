#!/usr/bin/env node

/**
 * OpenRouter integration that loads API key from .env file
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Load .env file
function loadEnv() {
  const envPath = path.join(__dirname, '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
    
    console.log('✅ Loaded API key from .env file');
  }
}

// Load environment variables
loadEnv();

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          reject(new Error(`Invalid JSON response: ${body}`));
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function queryOpenRouter(messages, apiKey, model = 'google/gemini-2.5-pro-preview') {
  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'X-Title': 'Shopify Development Assistant'
    }
  };

  const data = {
    model,
    messages,
    max_tokens: 4000
  };

  const response = await makeRequest('https://openrouter.ai/api/v1/chat/completions', options, data);
  
  if (response.error) {
    throw new Error(`OpenRouter API error: ${response.error.message}`);
  }

  return response.choices[0].message.content;
}

function getShopifyContext(query) {
  const shopifyContext = {
    'product': `
Shopify Product API Context:
- Products are created using the Admin GraphQL API
- Key fields: title, description, vendor, product_type, tags
- Products can have multiple variants with different prices, SKUs, inventory
- Use the productCreate mutation to create products

Example GraphQL mutation:
mutation productCreate($input: ProductInput!) {
  productCreate(input: $input) {
    product {
      id
      title
      handle
    }
    userErrors {
      field
      message
    }
  }
}
`,
    'order': `
Shopify Order API Context:
- Orders represent customer purchases
- Key fields: line_items, customer, shipping_address, billing_address
- Use the orders query to fetch orders
- Use fulfillmentCreate to fulfill orders

Example GraphQL query:
query getOrders($first: Int!) {
  orders(first: $first) {
    edges {
      node {
        id
        name
        totalPrice
        customer {
          email
        }
      }
    }
  }
}
`,
    'default': `
Shopify Development Context:
- Shopify provides Admin API, Storefront API, Partner API
- Admin API is for managing store data (products, orders, customers)
- Use GraphQL for modern development
- Authentication via access tokens
- Rate limits apply to all APIs
`
  };

  const lowerQuery = query.toLowerCase();
  if (lowerQuery.includes('product')) return shopifyContext.product;
  if (lowerQuery.includes('order')) return shopifyContext.order;
  return shopifyContext.default;
}

async function askShopifyQuestion(question, apiKey) {
  console.log(`\n🤔 Question: ${question}`);
  
  const context = getShopifyContext(question);
  
  const messages = [
    {
      role: 'system',
      content: `You are a Shopify development expert. Use the provided Shopify context to give accurate, helpful answers.

Shopify Context:
${context}

Provide practical examples and code snippets when relevant.`
    },
    {
      role: 'user',
      content: question
    }
  ];

  console.log('🤖 Getting response from Google Gemini 2.5 Pro Preview...');
  const response = await queryOpenRouter(messages, apiKey);
  
  console.log(`\n✅ Answer:\n${response}`);
  return response;
}

async function main() {
  const apiKey = process.env.OPENROUTER_API_KEY;
  
  if (!apiKey) {
    console.error('❌ Please set OPENROUTER_API_KEY');
    console.log('');
    console.log('Option 1: Set in .env file');
    console.log('  echo "OPENROUTER_API_KEY=your_key_here" > .env');
    console.log('');
    console.log('Option 2: Set as environment variable');
    console.log('  export OPENROUTER_API_KEY="your_key_here"');
    console.log('');
    console.log('Option 3: Run with the key inline');
    console.log('  OPENROUTER_API_KEY="your_key" npm run openrouter-env');
    console.log('');
    console.log('Get your API key from: https://openrouter.ai/keys');
    return;
  }

  console.log('🚀 Shopify + Google Gemini 2.5 Pro Preview');
  console.log('');

  const questions = [
    "How do I create a product using Shopify's GraphQL API?",
    "What's the difference between Admin API and Storefront API?",
    "Show me how to query orders with GraphQL"
  ];

  try {
    for (const question of questions) {
      await askShopifyQuestion(question, apiKey);
      console.log('\n' + '='.repeat(60));
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n🎉 Demo completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main().catch(console.error);
