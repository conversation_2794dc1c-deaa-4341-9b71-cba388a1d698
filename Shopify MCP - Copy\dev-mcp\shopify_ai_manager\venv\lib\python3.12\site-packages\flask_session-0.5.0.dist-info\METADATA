Metadata-Version: 2.1
Name: Flask-Session
Version: 0.5.0
Summary: Server-side session support for Flask
Author-email: <PERSON><PERSON> <<EMAIL>>
Maintainer-email: Pallets Community Ecosystem <<EMAIL>>
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Web Environment
Classifier: Framework :: Flask
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Topic :: Internet :: WWW/HTTP :: Session
Classifier: Topic :: Internet :: WWW/HTTP :: WSGI
Classifier: Topic :: Internet :: WWW/HTTP :: WSGI :: Application
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Requires-Dist: flask>=2.2
Requires-Dist: cachelib
Project-URL: Changes, https://flasksession.readthedocs.io/changes.html
Project-URL: Chat, https://discord.gg/pallets
Project-URL: Documentation, https://flasksession.readthedocs.io
Project-URL: Issue Tracker, https://github.com/pallets-eco/flask-session/issues/
Project-URL: Source Code, https://github.com/pallets-eco/flask-session/

Flask-Session
=============

Flask-Session is an extension for Flask that adds support for server-side sessions to
your application.

